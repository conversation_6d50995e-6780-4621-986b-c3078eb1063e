/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CouponMethod } from './CouponMethod';
import type { SubscriberType } from './SubscriberType';
import type { SubscriptionsDtoStatus } from './SubscriptionsDtoStatus';
export type SubscriptionDetailsResponse = {
    status: SubscriptionsDtoStatus;
    cancelAtPeriodEnd: boolean;
    expires?: string | null;
    couponAppliedForMonths?: number | null;
    couponAppliedForDays?: number | null;
    couponExpiresAt?: string | null;
    tierId?: string | null;
    type: SubscriberType;
    couponMethod?: CouponMethod;
    couponPercentOff?: number | null;
    isApple: boolean;
};


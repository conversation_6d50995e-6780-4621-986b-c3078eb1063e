/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Analytics } from './Analytics';
import type { CategoryResponse } from './CategoryResponse';
import type { Creator } from './Creator';
import type { DiscordResponse } from './DiscordResponse';
import type { GjirafaLivestreamMeta } from './GjirafaLivestreamMeta';
import type { ImageAsset } from './ImageAsset';
import type { NotificationsEnabled } from './NotificationsEnabled';
import type { Role } from './Role';
import type { SpotifyResponse } from './SpotifyResponse';
import type { TierResponse } from './TierResponse';
import type { UserDetailsCountsResponse } from './UserDetailsCountsResponse';
import type { UserProfileType } from './UserProfileType';
export type UserDetailsResponse = {
    id: string;
    name: string;
    bio: string;
    bioHtml: string;
    bioEn: string;
    bioHtmlEn: string;
    path: string;
    image?: ImageAsset;
    hasRssFeed: boolean;
    counts: UserDetailsCountsResponse;
    verified: boolean;
    subscribable: boolean;
    tier: TierResponse;
    categories: Array<CategoryResponse>;
    analytics: Analytics;
    isOfAge: boolean;
    role: Role;
    email?: string | null;
    creator: Creator;
    discord?: DiscordResponse;
    notificationSettings: NotificationsEnabled;
    language: string;
    spotify: SpotifyResponse;
    gjirafaLivestreamMeta?: GjirafaLivestreamMeta;
    profileType: UserProfileType;
    hasPostPreviews: boolean;
};


package hero.contract.api.dto

import hero.core.data.SimplePageResponse
import hero.model.Chapter
import hero.model.DocumentAsset
import hero.model.GjirafaAsset
import hero.model.GjirafaLiveAsset
import hero.model.ImageAssetDto
import hero.model.PostCounts
import hero.model.YouTubeAsset
import hero.model.topics.PostState
import java.time.Instant

data class PostResponse(
    val id: String,
    val state: PostState,
    val title: String?,
    val text: String?,
    val textHtml: String?,
    val textDelta: String?,
    val fullAsset: Boolean?,
    val excludeFromRss: Boolean?,
    val pinnedAt: Instant?,
    val assets: List<PostAssetResponse>,
    val previewAssets: List<PostPreviewAssetResponse>,
    val categories: List<CategoryResponse>,
    val counts: PostCounts,
    val publishedAt: Instant?,
    val price: Long?,
    val assetsCount: Int?,
    val savedPostInfo: SavedCreatorPostInfoResponse?,
    val relationships: PostRelationships,
    val chapters: List<Chapter>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val pollId: String?,
    val myVote: Int,
    val voteScore: Int,
    val hasPreview: Boolean,
)

data class PostRelationships(
    val userId: String,
    val parentId: String?,
    val siblingId: String?,
    val messageThreadId: String?,
)

data class SavedCreatorPostInfoResponse(val id: String, val savedAt: Instant)

data class PagedPostResponse(
    override val content: List<PostResponse>,
    override val hasNext: Boolean,
    override val afterCursor: String? = null,
    override val beforeCursor: String? = null,
) : SimplePageResponse<PostResponse>

data class CategoryResponse(
    val id: String,
    val name: String,
    val slug: String,
)

data class PostAssetResponse(
    val image: ImageAssetDto? = null,
    val youTube: YouTubeAsset? = null,
    val gjirafa: GjirafaAsset? = null,
    val gjirafaLive: GjirafaLiveAsset? = null,
    val document: DocumentAsset? = null,
    val thumbnail: String? = null,
    val bunnyAsset: String? = null,
    val audioAsset: String? = null,
    val thumbnailImage: ImageAssetDto? = null,
)

data class PostPreviewAssetResponse(
    val image: PreviewImageResponse? = null,
    val gjirafa: PreviewGjirafaResponse? = null,
)

data class PreviewImageResponse(
    val url: String,
    val width: Int,
    val height: Int,
)

data class PreviewGjirafaResponse(
    val previewStaticUrl: String,
    val previewAnimatedUrl: String,
    val previewStripUrl: String,
    val type: PreviewGjirafaType,
    val duration: Double,
)

enum class PreviewGjirafaType {
    AUDIO,
    VIDEO,
}

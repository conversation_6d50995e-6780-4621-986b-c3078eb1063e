package hero.stripe.account.service

import com.stripe.model.Account
import hero.baseutils.log
import hero.gcloud.PubSub
import hero.gcloud.TypedCollectionReference
import hero.gcloud.contains
import hero.gcloud.root
import hero.gcloud.where
import hero.jackson.toJson
import hero.model.Creator
import hero.model.Currency
import hero.model.User
import hero.model.UserStateChange
import hero.model.UserStateChanged
import hero.model.topics.EmailPublished
import hero.sql.jooq.tables.ConnectedAccount.CONNECTED_ACCOUNT
import hero.stripe.service.StripeService
import org.jooq.DSLContext
import java.time.Instant

class AccountCommandService(
    private val stripeService: StripeService,
    private val usersCollection: TypedCollectionReference<User>,
    private val pubSub: PubSub,
    private val hostname: String,
    lazyContext: Lazy<DSLContext>,
) {
    private val context: DSLContext by lazyContext

    fun execute(command: ProcessAccount) {
        log.info("Processing connected account ${command.accountId}", mapOf("accountId" to command.accountId))
        val account = stripeService.getAccount(command.accountId, command.currency)
        updateCreator(account, command.currency)
        saveAccount(account)
        log.info("Done processing account ${command.accountId}", mapOf("accountId" to command.accountId))
    }

    private fun updateCreator(
        account: Account,
        currency: Currency,
    ) {
        val user = findByConnectedAccountId(account.id)

        if (user == null) {
            log.info(
                "Stripe webhooks us for connected account ${account.id} " +
                    "for which user was not found or uses a new connected account.",
            )
            return
        }

        val requirements = stripeService.requirements(user, "webhook", currency)

        if (requirements.deleted) {
            log.fatal(
                "Manual action needed: User ${user.id} deleted their " +
                    "Stripe account ${requirements.stripeAccountId}.",
                mapOf("userId" to user.id, "accountId" to requirements.stripeAccountId),
            )
            return
        }

        val accountActive = requirements.valid
        val updatedCreator = user
            .creator.copy(
                stripeRequirements = requirements,
                stripeAccountActive = accountActive,
            ).let {
                if (!user.creator.stripeAccountOnboarded) {
                    it.copy(stripeAccountOnboarded = stripeService.onboardingFinished(user, currency))
                } else {
                    it
                }
            }

        // we care only about the first verification
        val verifiedAt = if (user.verifiedAt != null) {
            user.verifiedAt
        } else if (updatedCreator.verified && !user.creator.verified) {
            Instant.now()
        } else {
            null
        }

        val updatedUser = user.copy(creator = updatedCreator, verifiedAt = verifiedAt)
        usersCollection[user.id].set(updatedUser)

        if (user.creator.stripeAccountActive == accountActive) {
            log.info(
                "User's connected account unchanged.",
                mapOf("userId" to user.id, "state" to accountActive, "requirements" to requirements.toJson()),
            )
            return
        }

        if (user.creator.stripeAccountActive && !accountActive) {
            // stripe account was invalidated or disabled, support should verify if the user is not blocked
            pubSub.publish(
                EmailPublished(
                    to = "<EMAIL>",
                    // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                    template = "stripe-disabled",
                    variables = listOf(
                        "user-id" to user.id,
                        "user-name" to user.name,
                        "user-link" to "$hostname/${user.path}",
                        "stripe-account-id" to account.id,
                        "currently-due" to requirements.currentlyDue,
                        "past-due" to requirements.pastDue,
                        "disabled-reason" to requirements.disabledReason,
                    ),
                    language = "EN",
                ),
            )
        }

        log.info(
            "User's connected account state changed.",
            mapOf(
                "userId" to user.id,
                "statePrevious" to user.creator.stripeAccountActive,
                "stateNew" to accountActive,
                "requirements" to requirements.toJson(),
            ),
        )

        if (accountActive) {
            pubSub.publish(
                UserStateChanged(
                    stateChange = UserStateChange.PATCHED,
                    user = updatedUser,
                ),
            )
        }

        if (accountActive && !user.email.isNullOrEmpty()) {
            pubSub.publish(
                EmailPublished(
                    to = user.email!!,
                    // https://gitlab.com/-/ide/project/heroheroco/mailgun-templates/tree/main/-/templates/
                    template = "stripe-connected",
                    variables = listOf(
                        "user-name" to user.name,
                    ),
                    language = user.language,
                ),
            )
        }
    }

    private fun saveAccount(account: Account) {
        val creator = usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(account.id)
            .fetchSingle()
            ?: usersCollection
                .where(root(User::creator).path(Creator::stripeAccountLegacyIds)).contains(account.id)
                .fetchSingle()

        if (creator == null) {
            log.info("Could not find user for account ${account.id}")
            return
        }

        context
            .insertInto(CONNECTED_ACCOUNT)
            .set(CONNECTED_ACCOUNT.STRIPE_ID, account.id)
            .set(CONNECTED_ACCOUNT.USER_ID, creator.id)
            .set(CONNECTED_ACCOUNT.CREATED_AT, Instant.ofEpochSecond(account.created))
            .set(CONNECTED_ACCOUNT.DEFAULT_CURRENCY, account.defaultCurrency.uppercase())
            .set(CONNECTED_ACCOUNT.COUNTRY, account.country)
            .set(CONNECTED_ACCOUNT.TYPE, account.type)
            .set(CONNECTED_ACCOUNT.BUSINESS_NAME, account.businessProfile?.name)
            .set(CONNECTED_ACCOUNT.BUSINESS_EMAIL, account.businessProfile?.supportEmail)
            .set(CONNECTED_ACCOUNT.BUSINESS_PHONE, account.businessProfile?.supportPhone)
            .set(CONNECTED_ACCOUNT.BUSINESS_URL, account.businessProfile?.url)
            .set(CONNECTED_ACCOUNT.BUSINESS_TYPE, account.businessType?.uppercase())
            .onDuplicateKeyUpdate()
            .set(CONNECTED_ACCOUNT.DEFAULT_CURRENCY, account.defaultCurrency.uppercase())
            .set(CONNECTED_ACCOUNT.COUNTRY, account.country)
            .set(CONNECTED_ACCOUNT.TYPE, account.type)
            .set(CONNECTED_ACCOUNT.BUSINESS_NAME, account.businessProfile?.name)
            .set(CONNECTED_ACCOUNT.BUSINESS_EMAIL, account.businessProfile?.supportEmail)
            .set(CONNECTED_ACCOUNT.BUSINESS_PHONE, account.businessProfile?.supportPhone)
            .set(CONNECTED_ACCOUNT.BUSINESS_URL, account.businessProfile?.url)
            .set(CONNECTED_ACCOUNT.BUSINESS_TYPE, account.businessType?.uppercase())
            .execute()
    }

    private fun findByConnectedAccountId(accountId: String): User? =
        usersCollection
            .where(root(User::creator).path(Creator::stripeAccountId)).isEqualTo(accountId)
            .fetchSingle()
}

data class ProcessAccount(
    val accountId: String,
    val currency: Currency,
)

plugins {
    id("hero.kotlin-run-service-conventions")
}

val projectModule: (String) -> String by extra
dependencies {
    implementation(projectModule(":Modules:BaseUtils"))
    implementation(projectModule(":Modules:Http4k"))
    implementation(projectModule(":Modules:GoogleCloud"))
    implementation(projectModule(":Modules:Jwt"))
    implementation(projectModule(":Modules:Model"))
    implementation(projectModule(":Modules:Contract"))
    implementation(projectModule(":Modules:Exceptions"))
    implementation(projectModule(":Modules:Stripe"))
    implementation(projectModule(":Modules:Gjirafa"))
    implementation(projectModule(":Modules:SQL"))
    implementation(projectModule(":Modules:Repository"))
    implementation(projectModule(":Modules:Firebase"))

    testImplementation(projectModule(":Modules:IntegrationTesting"))
    testImplementation(projectModule(":Modules:Testing"))

    implementation("com.google.cloud:google-cloud-bigquery:_")
    implementation("com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:_")
    implementation("com.thedeanda:lorem:_")
    implementation("org.jsoup:jsoup:_")
}

package hero.api.post.controller.dto

import hero.api.post.service.CommentData
import hero.api.post.service.ReplyData
import hero.baseutils.minusDays
import hero.baseutils.plusDays
import hero.model.Post
import hero.model.PostCounts
import hero.model.Subscriber
import hero.model.SubscriberStatus
import hero.model.SubscriberType
import hero.model.topics.PostState
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.util.UUID

class PostDtoMappersTest {
    @Nested
    inner class PostDataToResponse {
        @Test
        fun `post is correctly anonymized`() {
            val post = post(userId = "cestmir")

            val response = post.toResponse(
                renderMeta = PostRenderMeta(
                    fullResponse = true,
                    paymentsFromUserIds = null,
                    showText = true,
                    isAuthor = true,
                    previewEnabled = false,
                    anonymize = true,
                ),
                myVote = null,
            )

            assertThat(response.text).isNotEqualTo(post.text)
            assertThat(response.textHtml).isNotEqualTo(post.textHtml)
            assertThat(response.textDelta).isNotEqualTo(post.textDelta)
            assertThat(response.text).isEqualTo("efficitur")
            assertThat(response.textHtml).isEqualTo("pellentesque")
            assertThat(response.textDelta).isEqualTo("inceptos")
        }

        @Test
        fun `post has correct preview length`() {
            val textHtml = """
                <p>
                  <strong>BKT #247 | Proč se Dynamo venku trápí? Více <a href="https://bomby.cz">zde</a>. Mohl by jet Tomek na dvacítky a cítí Flek šanci na olympiádu?</strong>
                </p>
                <p></p>
                <p>V dalším dílu BKT si Kuba popovídal se Němou a Ryšavkou. Kluci společně probrali gól Lauka proti Olomouci, unikát
                  16letého Petra Tomka ve Varech, výhru Plzně proti Pardubicím, venkovní trápení Dynama, tragickou produktivitu Kladna,
                  další kolaps Hradce proti Kometě, pozici trenéra Martince, nedělní zákroky do konce zápasu, účelný hokej Třince nebo
                  situace kolem Vrány ve Švédsku.</p>
                <p></p>
                <p><strong>(začátek nezkrácené části v čase 56:01)</strong></p>,
            """.trimIndent()
            val post = post(userId = "cestmir", hasPreview = true, textHtml = textHtml)

            val response = post.toResponse(
                renderMeta = PostRenderMeta(
                    fullResponse = false,
                    showText = false,
                    previewEnabled = true,
                    previewTextLength = 48,
                ),
                myVote = null,
            )

            assertThat(response.text).isNotEqualTo(post.text)
            assertThat(response.textHtml).isNotEqualTo(post.textHtml)
            assertThat(response.textDelta).isNotEqualTo(post.textDelta)
            assertThat(response.text).isEqualTo("BKT #247 | Proč se Dynamo venku trápí? Více zde.")
            assertThat(response.textHtml).isEqualTo(
                """
                <p><strong>BKT #247 | Proč se Dynamo venku trápí? Více zde.</strong></p>
                <p></p>
                <p></p>
                <p></p>
                <p><strong></strong></p>
                """.trimIndent(),
            )
            assertThat(response.textDelta).isNull()
        }
    }

    @Nested
    inner class CommentDataToResponse {
        @Test
        fun `post creator always has full access to all comments`() {
            val comment = CommentData(comment = post(userId = "petr"), rootPost = post(userId = "cest"), null, 0, null)

            val response = comment.toResponse("cest")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does has full access to root post if he has active subscription`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.ACTIVE, expiresAt = Instant.now().plusDays(1))
            val comment = CommentData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                subscriber,
                0,
                null,
            )

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does does not have full access to root post if his subscription expired`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.UNPAID, expiresAt = Instant.now().minusDays(1))
            val comment = CommentData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                subscriber,
                0,
                null,
            )

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isFalse()
        }

        @Test
        fun `user does not have full access to root post if is not a subscriber`() {
            val comment = CommentData(comment = post(userId = "petr"), rootPost = post(userId = "cest"), null, 0, null)

            val response = comment.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isFalse()
        }
    }

    @Nested
    inner class ReplyDataToResponse {
        @Test
        fun `post creator always has full access to all comments`() {
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                parent = post(userId = "filip"),
                subscriptionInfo = null,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("cest")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does has full access to root post if he has active subscription`() {
            val subscriber = subscriber("petr", "cest", SubscriberStatus.ACTIVE, expiresAt = Instant.now().plusDays(1))
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "cest"),
                parent = post(userId = "filip"),
                subscriptionInfo = subscriber,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isTrue()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does does not have full access to root post if his subscription expired`() {
            val subscriber = subscriber("filip", "ces", SubscriberStatus.UNPAID, expiresAt = Instant.now().minusDays(1))
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "ces"),
                parent = post(userId = "filip"),
                subscriptionInfo = subscriber,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("filip")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isTrue()
        }

        @Test
        fun `user does not have full access to root post if is not a subscriber`() {
            val reply = ReplyData(
                comment = post(userId = "petr"),
                rootPost = post(userId = "ces"),
                parent = post(userId = "filip"),
                subscriptionInfo = null,
                myVote = 0,
                isPartOfCommunity = null,
            )

            val response = reply.toResponse("petr")

            assertThat(response.comment.fullAsset).isTrue()
            assertThat(response.rootParent.fullAsset).isFalse()
            assertThat(response.parent.fullAsset).isTrue()
        }
    }
}

// TODO move this to some test module, since this does not belong to integration testing module
private fun post(
    id: String = UUID.randomUUID().toString(),
    userId: String = "user-id",
    parentId: String = "parent-id",
    textHtml: String = "text-html",
    hasPreview: Boolean = true,
) = Post(
    id = id,
    text = "text",
    textHtml = textHtml,
    textDelta = "text-delta",
    state = PostState.PUBLISHED,
    userId = userId,
    parentId = parentId,
    siblingId = "sibling-id",
    parentUserId = "parent-user-id",
    published = Instant.now(),
    pinnedAt = Instant.now(),
    messageThreadId = null,
    assets = listOf(),
    price = null,
    counts = PostCounts(),
    categories = listOf(),
    hasPreview = hasPreview,
    created = Instant.now(),
    updated = Instant.now(),
)

private fun subscriber(
    userId: String,
    creatorId: String,
    status: SubscriberStatus = SubscriberStatus.ACTIVE,
    expiresAt: Instant = Instant.now(),
) = Subscriber(
    userId = userId,
    creatorId = creatorId,
    couponMethod = null,
    status = status,
    subscribed = Instant.now(),
    expires = expiresAt,
    subscriberType = SubscriberType.STRIPE,
    tierId = "EUR05",
)

package hero.api.community.service

import hero.core.data.Page
import hero.core.data.PageRequest
import hero.core.data.Pageable
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.NotFoundException
import hero.model.Community
import hero.model.CommunityMemberStatus
import hero.repository.community.CommunityRepository
import hero.repository.community.JooqCommunityHelper
import hero.repository.community.fetchMemberCommunityIds
import hero.sql.jooq.Tables.COMMUNITY
import hero.sql.jooq.Tables.COMMUNITY_MEMBER
import org.jooq.DSLContext
import org.jooq.exception.NoDataFoundException
import java.util.UUID

class CommunityQueryService(
    lazyContext: Lazy<DSLContext>,
    private val communityRepository: CommunityRepository,
) {
    private val context by lazyContext

    fun execute(query: GetCommunity): CommunityWithMeta {
        if (query.communityId == null && query.slug == null) {
            throw BadRequestException("Either communityId or slug must be provided")
        }

        if (query.communityId != null && query.slug != null) {
            throw BadRequestException("Only one of communityId or slug must be provided")
        }

        val community = if (query.communityId != null) {
            communityRepository.getById(query.communityId)
        } else {
            context.select(JooqCommunityHelper.communityFields)
                .from(COMMUNITY)
                .where(COMMUNITY.SLUG.eq(query.slug))
                .runCatching { JooqCommunityHelper.mapRecordToEntity(fetchSingle()) }
                .getOrElse {
                    when (it) {
                        is NoDataFoundException -> throw NotFoundException()
                        else -> throw it
                    }
                }
        }
        val isCommunityMember = when {
            query.userId == null -> false

            community.ownerId == query.userId -> true

            else -> {
                val communityIds = context.fetchMemberCommunityIds(query.userId)
                community.id in communityIds
            }
        }

        return CommunityWithMeta(community, isCommunityMember)
    }

    fun execute(query: GetCommunities): Page<CommunityWithMeta> {
        if (query.filter.ownerId == null) {
            return Page.emptyPage()
        }

        val communities = context
            .select(JooqCommunityHelper.communityFields)
            .select(COMMUNITY_MEMBER.STATE)
            .from(COMMUNITY)
            .leftJoin(COMMUNITY_MEMBER)
            .on(COMMUNITY_MEMBER.COMMUNITY_ID.eq(COMMUNITY.ID)).and(COMMUNITY_MEMBER.USER_ID.eq(query.userId))
            .where(COMMUNITY.OWNER_ID.eq(query.filter.ownerId))
            .fetch()
            .map {
                val community = JooqCommunityHelper.mapRecordToEntity(it)
                val isMember = it[COMMUNITY_MEMBER.STATE] == CommunityMemberStatus.ACTIVE.name
                CommunityWithMeta(community, isMember)
            }

        return Page(communities, hasNext = false, nextPageable = PageRequest())
    }
}

data class GetCommunity(val communityId: UUID?, val slug: String?, val userId: String?)

data class GetCommunities(val userId: String?, val filter: GetCommunitiesFilter, val pageable: Pageable = PageRequest())

data class GetCommunitiesFilter(val ownerId: String?)

data class CommunityWithMeta(val community: Community, val isMember: Boolean)

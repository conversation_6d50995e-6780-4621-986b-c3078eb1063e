package hero.api.post.service.dto

import hero.model.DocumentAsset
import hero.model.YouTubeAsset

data class PostInput(
    val title: String?,
    val text: String,
    val textHtml: String,
    val assets: List<PostAssetInput> = listOf(),
    val textDelta: String? = null,
)

data class PostAssetInput(
    val image: ImageAssetInput? = null,
    val gjirafa: GjirafaAssetInput? = null,
    val gjirafaLivestream: GjirafaLivestreamAssetInput? = null,
    val document: DocumentAsset? = null,
    val thumbnail: String? = null,
    val thumbnailImage: ImageAssetInput? = null,
    val youtube: YouTubeAsset? = null,
)

data class GjirafaAssetInput(
    val id: String,
)

data class GjirafaLivestreamAssetInput(
    val id: String,
)

data class ImageAssetInput(
    val url: String,
    val width: Int,
    val height: Int,
    val fileName: String?,
    val fileSize: Long?,
    val hidden: Boolean = false,
)

package hero.api.post.service

import hero.api.post.service.dto.PostInput
import hero.baseutils.EnvironmentVariables
import hero.baseutils.SystemEnv
import hero.baseutils.log
import hero.baseutils.minus
import hero.core.logging.Logger
import hero.exceptions.http.BadRequestException
import hero.exceptions.http.ForbiddenException
import hero.gcloud.TypedCollectionReference
import hero.gcloud.where
import hero.model.Category
import hero.model.Post
import hero.model.topics.PostState.PUBLISHED
import hero.repository.community.CommunityRepository
import hero.repository.community.fetchMemberCommunityIds
import hero.repository.post.PostRepository
import hero.repository.post.PostType
import hero.sql.jooq.Tables
import hero.sql.jooq.Tables.COMMUNITY
import org.jooq.DSLContext
import java.time.Instant
import java.util.UUID
import kotlin.time.Duration.Companion.minutes

class CreatorPostCommandService(
    lazyContext: Lazy<DSLContext>,
    private val postRepository: PostRepository,
    private val communityRepository: CommunityRepository,
    private val postService: PostService,
    private val categoriesCollection: TypedCollectionReference<Category>,
    private val systemEnv: EnvironmentVariables = SystemEnv,
    private val logger: Logger = log,
) {
    private val context by lazyContext

    fun execute(command: CreateCreatorPost): Post {
        if (command.publishedAt?.isBefore(Instant.now() - 5.minutes) == true) {
            throw BadRequestException("Field publishedAt can be at most 5 minutes in the past")
        }
        validateCategories(command.categories, command.creatorId)
        validateCommunity(command.communityId, command.creatorId)

        val post = postService.execute(
            CreatePost(
                userId = command.creatorId,
                publishedAt = command.publishedAt ?: Instant.now(),
                categories = command.categories,
                title = command.attributes.title,
                text = command.attributes.text,
                textHtml = command.attributes.textHtml,
                assets = command.attributes.assets,
                parentUserId = command.creatorId,
                textDelta = command.attributes.textDelta,
                isAgeRestricted = command.isAgeRestricted,
                isSponsored = command.isSponsored,
                communityId = command.communityId,
                hasPreview = command.hasPreview,
            ),
        )
        logger.info("Creator ${command.creatorId} created a post ${post.id}", post.toLabels())

        return post
    }

    fun execute(command: UpdateCreatorPost): Post {
        if (command.publishedAt?.isBefore(Instant.now() - 5.minutes) == true) {
            throw BadRequestException("Field publishedAt can be at most 5 minutes in the past")
        }
        validateCategories(command.categories, command.userId)

        val post = postService.execute(
            UpdatePost(
                userId = command.userId,
                postId = command.postId,
                publishedAt = command.publishedAt,
                pinnedAt = command.pinnedAt,
                categories = command.categories.toList(),
                title = command.attributes.title,
                text = command.attributes.text,
                textDelta = command.attributes.textDelta,
                textHtml = command.attributes.textHtml,
                assets = command.attributes.assets,
                isAgeRestricted = command.isAgeRestricted,
                isSponsored = command.isSponsored,
                excludeFromRss = command.excludeFromRss,
                hasPreview = command.hasPreview,
                postValidator = { originalPost ->
                    if (originalPost.parentId != null || originalPost.messageThreadId != null) {
                        throw BadRequestException("Post ${command.postId} is not a post or a thread")
                    }

                    val isPinning = command.pinnedAt != null && originalPost.pinnedAt == null
                    val communityId = originalPost.communityId?.let { UUID.fromString(it) }
                    val isCommunity = communityId != null

                    // normal creator posts can be updated only by the author
                    if (originalPost.userId != command.userId && !isCommunity) {
                        throw ForbiddenException("Only post owner can update his posts")
                    }

                    // pinning community posts is allowed only by the community owner
                    // other members cannot modify other user's threads
                    if ((isPinning && isCommunity) || (originalPost.userId != command.userId && isCommunity)) {
                        val community = communityRepository.getById(communityId)
                        if (community.ownerId != command.userId) {
                            // maybe we should not allow editing texts and other stuff?
                            throw ForbiddenException("Only community owner can edit other user's threads")
                        }
                    }

                    // we allow pinning only three pinned posts
                    // we do not enforce this in prod for normal posts, but for community we do
                    if ((!systemEnv.isProduction || isCommunity) && isPinning) {
                        val pinnedPosts = postRepository.find {
                            this
                                .let {
                                    if (isCommunity) {
                                        it.where(Tables.POST.COMMUNITY_ID.eq(communityId))
                                    } else {
                                        it.where(Tables.POST.USER_ID.eq(command.userId))
                                            .and(Tables.POST.COMMUNITY_ID.isNull)
                                    }
                                }
                                .and(Tables.POST.STATE.eq(PUBLISHED.name))
                                .and(Tables.POST.TYPE.eq(PostType.CONTENT_POST.name))
                                .and(Tables.POST.PINNED_AT.isNotNull)
                        }

                        if (pinnedPosts.size >= 3) {
                            throw BadRequestException("Only three pinned posts are allowed.")
                        }
                    }
                },
            ),
        )
        logger.info("Creator ${command.userId} updated a post ${post.id}", post.toLabels())

        return post
    }

    private fun validateCommunity(
        communityId: UUID?,
        creatorId: String,
    ) {
        if (communityId == null) {
            return
        }
        val community = context.selectFrom(COMMUNITY)
            .where(COMMUNITY.ID.eq(communityId))
            .fetchSingle()

        if (creatorId == community.ownerId) {
            return
        }

        val communityIds = context.fetchMemberCommunityIds(creatorId)
        if (communityId !in communityIds) {
            throw ForbiddenException(
                "User $creatorId cannot post in community $communityId",
            )
        }
    }

    private fun validateCategories(
        categories: Set<String>,
        userId: String,
    ) {
        if (categories.isEmpty()) {
            return
        }

        val userCategories = categoriesCollection
            .where(Category::userId).isEqualTo(userId)
            .fetchAll()
            .map { it.id }
            .toSet()

        val invalidCategories = categories - userCategories
        if (invalidCategories.isNotEmpty()) {
            throw BadRequestException(
                "Invalid categories $invalidCategories, creator '$userId' does not have these categories",
            )
        }
    }
}

data class CreateCreatorPost(
    val creatorId: String,
    val attributes: PostInput,
    val categories: Set<String>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val communityId: UUID?,
    val publishedAt: Instant? = null,
    val hasPreview: Boolean,
)

data class UpdateCreatorPost(
    val userId: String,
    val postId: String,
    val attributes: PostInput,
    val categories: Set<String>,
    val isAgeRestricted: Boolean,
    val isSponsored: Boolean,
    val excludeFromRss: Boolean?,
    val publishedAt: Instant? = null,
    val pinnedAt: Instant? = null,
    val hasPreview: Boolean,
)

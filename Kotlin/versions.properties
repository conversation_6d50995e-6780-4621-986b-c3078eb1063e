#### Dependencies and Plugin versions with their available updates.
#### Generated by `./gradlew refreshVersions` version 0.60.6
####
#### Don't manually edit or split the comments that start with four hashtags (####),
#### they will be overwritten by refreshVersions.
####
#### suppress inspection "SpellCheckingInspection" for whole file
#### suppress inspection "UnusedProperty" for whole file

# todo upgrade to v9
plugin.com.gradleup.shadow=8.3.9

plugin.io.sentry.jvm.gradle=5.9.0

plugin.nu.studer.jooq=10.1.1

plugin.org.flywaydb.flyway=11.11.2

plugin.org.jlleitschuh.gradle.ktlint=13.1.0

version.assertj.core=3.27.4

version.com.apple.itunes.storekit..app-store-server-library=3.6.0

version.com.fasterxml.jackson.module..jackson-module-kotlin=2.20.0

version.com.fasterxml.jackson.datatype..jackson-datatype-jsr310=2.20.0

version.com.github.doyaaaaaken..kotlin-csv-jvm=1.10.0

version.com.github.kittinunf.fuel..fuel-jackson=2.3.1

version.com.github.kittinunf.fuel..fuel=2.3.1

version.com.google.cloud..google-cloud-firestore=3.32.2

version.com.google.api-client..google-api-client=2.8.1

version.com.google.apis..google-api-services-sheets=v4-rev20250616-2.0.0

version.com.google.apis..google-api-services-drive=v3-rev20250819-2.0.0

version.com.google.auth..google-auth-library-oauth2-http=1.38.0

version.com.google.auth..google-auth-library-credentials=1.38.0

version.com.google.cloud..google-cloud-vertexai=1.33.0

version.com.google.cloud..google-cloudevent-types=0.17.1

version.com.google.cloud.functions.invoker..java-function-invoker=1.4.1

version.com.google.cloud.functions..functions-framework-api=1.1.4

version.com.google.cloud..google-cloud-bigquery=2.54.2

version.com.google.cloud..google-cloud-logging=3.23.3

version.com.google.cloud..google-cloud-pubsub=1.141.3

version.com.google.cloud..google-cloud-vision=3.71.0

version.com.google.cloud..libraries-bom=26.67.0

version.com.google.cloud.sql..postgres-socket-factory=1.25.3

version.com.googlecode.owasp-java-html-sanitizer..owasp-java-html-sanitizer=20240325.1

version.com.jsoizo..kotlin-csv-jvm=1.10.0

# bumping to 29.0.0 requires massive refactoring, see:
# https://docs.stripe.com/changelog/basil/2025-03-31/add-support-for-multiple-partial-payments-on-invoices
version.com.stripe..stripe-java=28.4.0

version.com.sun.mail..javax.mail=1.6.2

version.com.thedeanda..lorem=2.2

version.com.vdurmont..emoji-java=5.1.1

# todo upgarde to v7
version.com.zaxxer..HikariCP=6.3.2

version.commons-codec..commons-codec=1.19.0

version.dev.fuxing..airtable-api=0.3.2

version.firebase-admin=9.5.0

version.hamcrest=3.0

version.http4k=********

version.io.gitlab.arturbosch.detekt..detekt-api=1.23.8

version.io.gitlab.arturbosch.detekt..**********************.23.8

version.io.gitlab.arturbosch.detekt..detekt-test=1.23.8

# when changing this, check the otel agent version in Kotlin/Run/Dockerfile
version.io.opentelemetry..opentelemetry-api=1.53.0

version.io.opentelemetry..opentelemetry-exporter-otlp=1.53.0

version.io.opentelemetry..opentelemetry-extension-trace-propagators=1.53.0

version.junit.jupiter=5.13.4

version.javax.validation..validation-api=2.0.1.Final

version.jakarta.xml.bind..jakarta.xml.bind-api=4.0.2

version.kotlinx.coroutines=1.10.2

version.kotlin=2.2.10

version.mockk=1.14.5

version.org.apache.logging.log4j..log4j-slf4j2-impl=2.25.1

version.org.flywaydb..flyway-core=11.11.2

version.org.flywaydb..flyway-database-postgresql=11.11.2

version.org.jooq..jooq=3.20.6

version.org.jooq..jooq-postgres-extensions=3.20.6

version.org.jsoup..jsoup=1.21.2

version.org.junit.platform..junit-platform-launcher=1.13.4

version.org.passay..passay=1.6.6

version.org.postgresql..postgresql=42.7.7

version.org.testcontainers..gcloud=1.21.3

version.org.testcontainers..postgresql=1.21.3

version.org.glassfish.jaxb..jaxb-runtime=4.0.5

version.org.apache.logging.log4j..log4j-web=2.25.1

version.org.apache.logging.log4j..log4j-core=2.25.1

version.org.apache.logging.log4j..log4j-api=2.25.1

version.org.apache.commons..commons-text=1.14.0

version.io.jsonwebtoken..jjwt-jackson=0.13.0

version.io.jsonwebtoken..jjwt-impl=0.13.0

version.io.jsonwebtoken..jjwt-api=0.13.0

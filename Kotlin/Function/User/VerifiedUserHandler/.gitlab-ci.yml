Kotlin/Function/User/VerifiedUserHandler/build:
  stage: build-services
  needs:
    - Kotlin/Function/Subscriber/build
    - Kotlin/Modules/BaseUtils/build
    - Kotlin/Modules/Core/build
    - Kotlin/Modules/GoogleCloud/build
    - Kotlin/Modules/IntegrationTesting/build
    - Kotlin/Modules/Model/build
    - Kotlin/Modules/Repository/build
    - Kotlin/Modules/Stripe/build
  extends:
    - .Kotlin/job-build-gradle-module
    - .Function/variables-build

.Kotlin/Function/User/VerifiedUserHandler/variables:
  variables:
    FUNCTION_NAME: "verified-user-handler"
    CLASS_NAME: "hero.functions.VerifiedUserHandler"
    TOPIC: "UserStateChanged"

Kotlin/Function/User/VerifiedUserHandler/deploy-devel:
  needs:
    - Kotlin/Function/User/VerifiedUserHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-devel
    - .Kotlin/Function/User/VerifiedUserHandler/variables

Kotlin/Function/User/VerifiedUserHandler/deploy-staging:
  needs:
    - Kotlin/Function/User/VerifiedUserHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-staging
    - .Kotlin/Function/User/VerifiedUserHandler/variables

Kotlin/Function/User/VerifiedUserHandler/deploy-prod:
  needs:
    - Kotlin/Function/User/VerifiedUserHandler/build
  extends:
    - .Kotlin/Function/deploy
    - .Function/variables-prod
    - .Kotlin/Function/User/VerifiedUserHandler/variables
